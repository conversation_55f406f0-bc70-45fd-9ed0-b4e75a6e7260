"""
轻量级机器学习引擎模块
封装scikit-learn模型的加载、训练和预测接口
"""

import os
import joblib
import logging
from typing import List, Dict, Any, Optional
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.pipeline import Pipeline

logger = logging.getLogger(__name__)


class MLEngine:
    def __init__(self, model_path: Optional[str] = None):
        self.model_path = model_path
        self.model: Optional[Pipeline] = None
        self.vectorizer = TfidfVectorizer()
        self.classifier = RandomForestClassifier(n_estimators=100, random_state=42)

    def is_ready(self) -> bool:
        """检查模型是否已加载并可用"""
        return self.model is not None

    def train(self, X_texts: List[str], y_labels: List[str]) -> None:
        """
        训练模型

        Args:
            X_texts: 文本特征列表
            y_labels: 标签列表
        """
        # 使用Pipeline封装向量化和分类器
        self.model = Pipeline([
            ('tfidf', self.vectorizer),
            ('clf', self.classifier)
        ])
        self.model.fit(X_texts, y_labels)

    def save_model(self, save_path: Optional[str] = None) -> None:
        """
        保存模型到文件

        Args:
            save_path: 模型保存路径，默认使用初始化路径
        """
        path = save_path or self.model_path
        if path and self.model:
            os.makedirs(os.path.dirname(path), exist_ok=True)
            joblib.dump(self.model, path)

    def load_model(self, load_path: Optional[str] = None) -> None:
        """
        从文件加载模型

        Args:
            load_path: 模型文件路径，默认使用初始化路径
        """
        path = load_path or self.model_path
        if not path:
            logger.error("未指定模型路径")
            self.model = None
            return

        try:
            path = os.path.abspath(path)
            logger.info(f"尝试加载模型文件(绝对路径): {path}")

            if not os.path.exists(path):
                logger.error(f"模型文件不存在: {path}")
                self.model = None
                return

            if not os.access(path, os.R_OK):
                logger.error(f"模型文件不可读: {path}")
                self.model = None
                return

            logger.info("开始加载模型...")
            self.model = joblib.load(path)
            logger.info("模型加载成功")

            if not hasattr(self.model, 'predict'):
                logger.error("加载的模型无效: 缺少predict方法")
                self.model = None

        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}", exc_info=True)
            self.model = None

    def predict_tags(self, file_info: Dict[str, Any]) -> List[str]:
        """
        根据文件信息预测标签

        Args:
            file_info: 文件信息字典，包含name, path, extension等

        Returns:
            预测标签列表
        """
        if not self.model:
            return []

        features = self.extract_features(file_info)
        if not features:
            return []

        predictions = self.model.predict([features])
        return self.decode_predictions(predictions)

    def extract_features(self, file_info: Dict[str, Any]) -> str:
        """
        提取文本特征，供模型使用

        Args:
            file_info: 文件信息字典

        Returns:
            文本特征字符串
        """
        # 增强特征提取：文件名、扩展名、路径、大小、修改时间等
        name = file_info.get("name", "")
        extension = file_info.get("extension", "")
        path = file_info.get("path", "")
        size = str(file_info.get("size", ""))
        modified_time = str(file_info.get("modified_time", ""))
        features = f"{name} {extension} {path} {size} {modified_time}"
        return features

    def decode_predictions(self, predictions: List[Any]) -> List[str]:
        """
        将模型预测结果转换为标签列表

        Args:
            predictions: 模型预测结果

        Returns:
            标签列表
        """
        # 假设预测结果为标签字符串列表
        if isinstance(predictions, (list, tuple)):
            return list(predictions)
        else:
            return [str(predictions)]
