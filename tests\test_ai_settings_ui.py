import pytest
from PySide6.QtWidgets import QApplication
from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage

@pytest.fixture(scope="module")
def app():
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    yield app

@pytest.fixture
def ai_settings_page(app):
    page = AISettingsPage()
    page.setup_ui()
    return page

def test_initial_state(ai_settings_page):
    # 测试初始状态，所有控件存在且默认值正确
    features_widget = ai_settings_page.features_widget
    assert features_widget is not None

    # AI总开关默认关闭
    assert not features_widget.ai_enabled_checkbox.isChecked()

    # 轻量级机器学习开关默认关闭
    assert not features_widget.ml_enabled_checkbox.isChecked()

def test_enable_disable_ai_features(ai_settings_page):
    features_widget = ai_settings_page.features_widget

    # 启用AI总开关，子控件应启用
    features_widget.ai_enabled_checkbox.setChecked(True)
    assert features_widget.project_enabled_checkbox.isEnabled()
    assert features_widget.ml_enabled_checkbox.isEnabled()

    # 禁用AI总开关，子控件应禁用
    features_widget.ai_enabled_checkbox.setChecked(False)
    assert not features_widget.project_enabled_checkbox.isEnabled()
    assert not features_widget.ml_enabled_checkbox.isEnabled()

def test_load_save_config(ai_settings_page):
    features_widget = ai_settings_page.features_widget

    # 构造配置字典
    config = {
        'enabled': True,
        'features': {
            'ml_basic': {
                'enabled': True,
                'model_path': 'models/test_model.pkl',
                'confidence_threshold': 0.75
            }
        }
    }

    # 加载配置
    features_widget.load_config(config)
    assert features_widget.ai_enabled_checkbox.isChecked()
    assert features_widget.ml_enabled_checkbox.isChecked()
    assert features_widget.ml_model_path_edit.text() == 'models/test_model.pkl'
    assert abs(features_widget.ml_confidence_threshold_spinbox.value() - 0.75) < 0.01

    # 修改配置并保存
    features_widget.ml_enabled_checkbox.setChecked(False)
    features_widget.ml_model_path_edit.setText('models/updated_model.pkl')
    features_widget.ml_confidence_threshold_spinbox.setValue(0.85)
    saved_config = features_widget.save_config()
    assert saved_config['enabled'] == features_widget.ai_enabled_checkbox.isChecked()
    assert saved_config['features']['ml_basic']['enabled'] == False
    assert saved_config['features']['ml_basic']['model_path'] == 'models/updated_model.pkl'
    assert abs(saved_config['features']['ml_basic']['confidence_threshold'] - 0.85) < 0.01
