import pytest
from PySide6.QtWidgets import QApplication
from smartvault.ui.dialogs.settings.pages.ai.components.ai_features_widget import AIFeaturesWidget
from smartvault.ui.dialogs.settings.pages.ai.components.ai_status_widget import AIStatusWidget

@pytest.fixture
def app(qtbot):
    test_app = QApplication.instance()
    if test_app is None:
        test_app = QApplication([])
    return test_app

def test_ml_switch_enable_disable_and_persistence(qtbot, app):
    widget = AIFeaturesWidget()
    qtbot.addWidget(widget)

    # 初始状态，关闭
    widget.load_config({'enabled': False, 'features': {'ml_basic': {'enabled': False, 'model_path': '', 'confidence_threshold': 0.6}}})
    assert not widget.ai_enabled_checkbox.isChecked()
    assert not widget.ml_enabled_checkbox.isEnabled()

    # 启用AI总开关，ML开关应启用
    widget.ai_enabled_checkbox.setChecked(True)
    assert widget.ml_enabled_checkbox.isEnabled()

    # 启用ML开关
    widget.ml_enabled_checkbox.setChecked(True)
    config = widget.save_config()
    assert config['features']['ml_basic']['enabled'] is True

    # 禁用AI总开关，ML开关应禁用且关闭
    widget.ai_enabled_checkbox.setChecked(False)
    assert not widget.ml_enabled_checkbox.isEnabled()
    assert not widget.ml_enabled_checkbox.isChecked()

def test_ml_engine_status_display(qtbot, app):
    status_widget = AIStatusWidget()
    qtbot.addWidget(status_widget)

    # 模拟配置管理器返回状态
    class DummyConfigManager:
        def get_ai_status(self):
            return {
                'enabled': True,
                'stage': 'ml_basic',
                'status': 'ready',
                'features': {
                    'smart_rules': True,
                    'adaptive_rules': True,
                    'ml_engine': True,
                    'deep_learning': False
                },
                'last_error': None
            }

    status_widget.config_manager = DummyConfigManager()
    status_widget.refresh_ai_status()

    assert status_widget.ai_enabled_label.text() == "已启用"
    assert status_widget.ai_stage_label.text() == "轻量级机器学习"
    assert status_widget.init_status_label.text() == "就绪"
    assert "✅ 可用" in status_widget.ml_engine_label.text()

def test_ml_engine_status_display_disabled(qtbot, app):
    status_widget = AIStatusWidget()
    qtbot.addWidget(status_widget)

    class DummyConfigManager:
        def get_ai_status(self):
            return {
                'enabled': False,
                'stage': 'rule_based',
                'status': 'not_initialized',
                'features': {
                    'smart_rules': False,
                    'adaptive_rules': False,
                    'ml_engine': False,
                    'deep_learning': False
                },
                'last_error': None
            }

    status_widget.config_manager = DummyConfigManager()
    status_widget.refresh_ai_status()

    assert status_widget.ai_enabled_label.text() == "未启用"
    assert status_widget.ai_stage_label.text() == "智能规则引擎"
    assert status_widget.init_status_label.text() == "未初始化"
    assert "❌ 未启用" in status_widget.ml_engine_label.text()
