"""
AI设置组件基类

为所有AI设置组件提供统一的基础功能
预期代码长度: < 150行
当前代码长度: 95行 ✅
"""

from abc import abstractmethod
from typing import Dict
from PySide6.QtWidgets import QGroupBox
from PySide6.QtCore import Signal


class BaseAIWidget(QGroupBox):
    """AI设置组件基类"""
    
    # 信号定义
    config_changed = Signal(dict)  # 配置变更信号
    status_update_requested = Signal()  # 状态更新请求信号
    
    def __init__(self, title: str, parent=None):
        """初始化基础AI组件
        
        Args:
            title: 组件标题
            parent: 父窗口
        """
        super().__init__(title, parent)
        self.config_manager = None
        self._config = {}
        self.setup_ui()
        self.setup_connections()
    
    def set_config_manager(self, manager):
        """设置配置管理器
        
        Args:
            manager: AI配置管理器实例
        """
        self.config_manager = manager
    
    @abstractmethod
    def setup_ui(self):
        """设置UI界面（子类必须实现）"""
        pass
    
    def setup_connections(self):
        """设置信号连接（子类可重写）"""
        pass
    
    @abstractmethod
    def load_config(self, config: Dict):
        """加载配置到UI控件（子类必须实现）
        
        Args:
            config: AI配置字典
        """
        pass
    
    @abstractmethod
    def save_config(self) -> Dict:
        """从UI控件保存配置（子类必须实现）
        
        Returns:
            Dict: 配置字典
        """
        pass
    
    def get_config(self) -> Dict:
        """获取当前配置
        
        Returns:
            Dict: 当前配置字典
        """
        return self._config.copy()
    
    def update_config(self, config: Dict):
        """更新配置
        
        Args:
            config: 新的配置字典
        """
        self._config.update(config)
        self.config_changed.emit(self._config)
    
    def refresh_status(self):
        """刷新状态显示"""
        self.status_update_requested.emit()
    
    def show_error(self, message: str):
        """显示错误信息
        
        Args:
            message: 错误信息
        """
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(self, "错误", message)
    
    def show_info(self, message: str):
        """显示提示信息
        
        Args:
            message: 提示信息
        """
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, "提示", message)
