import sys
import os
sys.path.append('.')

try:
    from smartvault.utils.config import load_config, get_config_path
    
    config_path = get_config_path()
    print(f"配置文件路径: {config_path}")
    print(f"配置文件存在: {os.path.exists(config_path)}")
    
    config = load_config()
    library_path = config.get("library_path", "未设置")
    print(f"文件库路径: {library_path}")
    print(f"文件库路径存在: {os.path.exists(library_path) if library_path != '未设置' else False}")
    
    # 检查数据库路径
    if library_path != "未设置":
        db_path = os.path.join(library_path, "data", "smartvault.db")
        print(f"数据库路径: {db_path}")
        print(f"数据库文件存在: {os.path.exists(db_path)}")
        print(f"数据库目录存在: {os.path.exists(os.path.dirname(db_path))}")
    
except Exception as e:
    print(f"检查失败: {e}")
    import traceback
    traceback.print_exc()
