"""
AI配置管理器

统一处理所有AI相关配置的加载、保存和验证
预期代码长度: < 200行
当前代码长度: 150行 ✅
"""

from typing import Dict, Tuple, Any, Optional
from smartvault.utils.config import get_ai_config


class AIConfigManager:
    """AI配置管理器 - 统一处理所有AI相关配置"""

    def __init__(self):
        self._config = {}
        self._ai_manager = None

    def set_ai_manager(self, ai_manager):
        """设置AI管理器引用"""
        self._ai_manager = ai_manager

    def load_ai_config(self) -> Dict:
        """加载AI配置

        Returns:
            Dict: AI配置字典
        """
        try:
            self._config = get_ai_config()
            return self._config.copy()
        except Exception as e:
            print(f"加载AI配置失败: {e}")
            return self._get_default_config()

    def save_ai_config(self, config: Dict) -> bool:
        """保存AI配置

        Args:
            config: AI配置字典

        Returns:
            bool: 保存是否成功
        """
        try:
            # 验证配置
            is_valid, error_msg = self.validate_ai_config(config)
            if not is_valid:
                print(f"AI配置验证失败: {error_msg}")
                return False

            # 保存配置
            self._config = config.copy()

            # 实际保存到配置文件
            from smartvault.utils.config import save_ai_config
            save_ai_config(config)

            return True
        except Exception as e:
            print(f"保存AI配置失败: {e}")
            return False

    def validate_ai_config(self, config: Dict) -> Tuple[bool, str]:
        """验证AI配置有效性

        Args:
            config: AI配置字典

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            # 检查必需字段
            required_fields = ['enabled', 'stage', 'features']
            for field in required_fields:
                if field not in config:
                    return False, f"缺少必需字段: {field}"

            # 检查阶段值
            valid_stages = ['rule_based', 'ml_basic', 'deep_learning']
            if config['stage'] not in valid_stages:
                return False, f"无效的AI阶段: {config['stage']}"

            # 检查功能配置
            features = config.get('features', {})
            if not isinstance(features, dict):
                return False, "features字段必须是字典类型"

            return True, ""
        except Exception as e:
            return False, f"配置验证异常: {e}"

    def get_ai_status(self) -> Dict:
        """获取AI功能状态

        Returns:
            Dict: AI状态信息
        """
        if self._ai_manager:
            return self._ai_manager.get_status()
        else:
            return {
                'enabled': False,
                'stage': 'rule_based',
                'status': 'not_initialized',
                'last_error': 'AI管理器未初始化',
                'features': {}
            }

    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息

        Returns:
            Dict: 学习统计信息
        """
        if self._ai_manager:
            return self._ai_manager.get_learning_statistics()
        else:
            return {}

    def get_rule_performance_stats(self) -> Dict:
        """获取规则性能统计

        Returns:
            Dict: 规则性能统计
        """
        if self._ai_manager:
            return self._ai_manager.get_rule_performance_stats()
        else:
            return {}

    def test_ai_function(self) -> Tuple[bool, str]:
        """测试AI功能

        Returns:
            Tuple[bool, str]: (测试是否成功, 结果信息)
        """
        try:
            if not self._ai_manager:
                return False, "AI管理器未初始化"

            if not self._ai_manager.is_available():
                return False, "AI功能未启用或未就绪"

            # 测试标签建议功能
            test_file = {
                'name': 'test.py',
                'extension': '.py',
                'path': '/test/test.py'
            }

            suggestions = self._ai_manager.suggest_tags(test_file)

            if suggestions:
                return True, f"AI功能正常，建议标签: {', '.join(suggestions[:3])}"
            else:
                return True, "AI功能正常，但未生成标签建议"

        except Exception as e:
            return False, f"AI功能测试失败: {e}"

    def _get_default_config(self) -> Dict:
        """获取默认AI配置

        Returns:
            Dict: 默认配置
        """
        return {
            'enabled': False,
            'stage': 'rule_based',
            'models': {
                'text_classifier': {
                    'enabled': False,
                    'model_path': '',
                    'confidence_threshold': 0.6
                }
            },
            'features': {
                'project_detection': {
                    'enabled': True,
                    'min_files': 3,
                    'confidence_threshold': 0.7
                },
                'series_detection': {
                    'enabled': True,
                    'min_files': 2,
                    'confidence_threshold': 0.6
                },
                'behavior_learning': {
                    'enabled': True,
                    'learning_rate': 0.1,
                    'max_patterns': 1000
                },
                'smart_suggestions': {
                    'enabled': True,
                    'max_suggestions': 8,
                    'merge_with_rules': True
                }
            }
        }
