import pytest
from unittest.mock import MagicMock, patch
from smartvault.services.ai.ai_manager import AIManager

def test_initialize_success():
    config = {
        "ai": {
            "enabled": True,
            "enable_ml": True,
            "models": {
                "text_classifier": {
                    "model_path": "dummy_path"
                }
            }
        }
    }
    manager = AIManager()
    with patch("smartvault.services.ai.ml_engine.MLEngine") as MockMLEngine:
        mock_ml_instance = MockMLEngine.return_value
        mock_ml_instance.load_model.return_value = None
        result = manager.initialize(config)
        assert result is True
        assert manager.enabled is True
        assert manager.current_stage == "ml_basic"
        assert manager.ml_engine is not None
        mock_ml_instance.load_model.assert_called_once()

def test_initialize_disabled():
    config = {
        "ai": {
            "enabled": False
        }
    }
    manager = AIManager()
    result = manager.initialize(config)
    assert result is True
    assert manager.enabled is False
    assert manager.ml_engine is None

def test_get_status():
    manager = AIManager()
    manager.enabled = True
    manager.current_stage = "ml_basic"
    manager.initialization_status = "ready"
    manager.ml_engine = MagicMock()
    status = manager.get_status()
    assert status["enabled"] is True
    assert status["stage"] == "ml_basic"
    assert status["status"] == "ready"
    assert status["features"]["ml_engine"] is True

def test_suggest_tags_with_ml(monkeypatch):
    manager = AIManager()
    manager.enabled = True
    manager.current_stage = "ml_basic"
    mock_ml_engine = MagicMock()
    mock_ml_engine.predict_tags.return_value = ["tag_ml"]
    manager.ml_engine = mock_ml_engine
    manager.smart_rule_engine = MagicMock()
    manager.smart_rule_engine.suggest_tags.return_value = ["tag_rule"]

    file_info = {"name": "file.txt"}
    # Patch fallback_service to avoid fallback
    manager.fallback_service = MagicMock()
    manager.fallback_service.suggest_tags_fallback.return_value = []

    # Patch smart_rule_engine.suggest_tags to return empty list to simulate no rule suggestions
    manager.smart_rule_engine.suggest_tags.return_value = []

    tags = manager.suggest_tags(file_info)
    # Since smart_rule_engine returns empty, tags should only contain ml tags
    assert "tag_ml" in tags
    assert "tag_rule" not in tags

def test_suggest_tags_fallback(monkeypatch):
    manager = AIManager()
    manager.enabled = True
    manager.ml_engine = None
    manager.smart_rule_engine = None
    manager.fallback_service = MagicMock()
    manager.fallback_service.suggest_tags_fallback.return_value = ["fallback_tag"]

    file_info = {"name": "file.txt"}
    tags = manager.suggest_tags(file_info)
    assert tags == ["fallback_tag"]

def test_initialize_exception(monkeypatch):
    manager = AIManager()
    def raise_exception(*args, **kwargs):
        raise Exception("fail")
    monkeypatch.setattr(manager, "smart_rule_engine", None)
    monkeypatch.setattr(manager, "adaptive_rule_engine", None)
    monkeypatch.setattr(manager, "ml_engine", None)
    monkeypatch.setattr(manager, "fallback_service", None)
    with patch("smartvault.services.ai.SmartRuleEngine", side_effect=raise_exception):
        result = manager.initialize({"ai": {"enabled": True}})
        assert result is False
        assert manager.initialization_status == "error"
        assert "fail" in manager.last_error
