import pytest
from unittest.mock import MagicMock, patch
from smartvault.services.ai.ai_manager import AIManager

@pytest.fixture
def ai_manager():
    manager = AIManager()
    # 初始化时禁用AI
    config = {
        "ai": {
            "enabled": False,
            "stage": "rule_based"
        }
    }
    manager.initialize(config)
    return manager

def test_ai_enabled_switch(ai_manager):
    # 初始状态应为禁用
    assert not ai_manager.is_available()

    # 启用AI功能
    config = {
        "ai": {
            "enabled": True,
            "stage": "rule_based"
        }
    }
    ai_manager.initialize(config)
    assert ai_manager.is_available()

def test_smart_rule_engine_suggest_tags(ai_manager):
    # 启用AI
    config = {
        "ai": {
            "enabled": True,
            "stage": "rule_based"
        }
    }
    ai_manager.initialize(config)

    file_info = {"name": "test_file.py", "path": "/path/to/test_file.py", "extension": ".py"}
    tags = ai_manager.suggest_tags(file_info)
    assert isinstance(tags, list)

def test_adaptive_rule_engine_feedback(ai_manager):
    config = {
        "ai": {
            "enabled": True,
            "stage": "rule_based"
        }
    }
    ai_manager.initialize(config)

    action_data = {
        "rule_feedback": True,
        "rule_id": "rule_123",
        "feedback": "accept",
        "context": {}
    }
    # 调用学习方法不应抛异常
    ai_manager.learn_from_user_action(action_data)

def test_fallback_on_error(ai_manager):
    config = {
        "ai": {
            "enabled": True,
            "stage": "rule_based"
        }
    }
    ai_manager.initialize(config)

    # 模拟smart_rule_engine抛异常，触发降级
    with patch.object(ai_manager.smart_rule_engine, "suggest_tags", side_effect=Exception("Test error")):
        file_info = {"name": "file.txt"}
        tags = ai_manager.suggest_tags(file_info)
        assert isinstance(tags, list)

def test_ml_engine_placeholder(ai_manager):
    # 测试ml_engine未实现时的行为
    config = {
        "ai": {
            "enabled": True,
            "stage": "ml_basic"
        }
    }
    ai_manager.initialize(config)
    file_info = {"name": "file.txt"}
    tags = ai_manager.suggest_tags(file_info)
    assert isinstance(tags, list)

# 性能和降级测试建议由集成环境执行，示例不包含

if __name__ == "__main__":
    pytest.main()
