"""
AI功能统一管理器

负责协调所有AI功能，提供统一的接口给其他服务使用
"""

import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .fallback_service import FallbackService
from .smart_rule_engine import SmartRuleEngine
from .adaptive_rule_engine import AdaptiveRuleEngine

logger = logging.getLogger(__name__)

class AIManager:
    """AI功能统一管理器"""

    def __init__(self):
        self.enabled = False
        self.current_stage = "rule_based"  # rule_based, ml_basic, deep_learning
        self.smart_rule_engine = None
        self.adaptive_rule_engine = None
        self.ml_engine = None
        self.fallback_service = FallbackService()

        # 与现有服务的集成
        self.tag_service = None
        self.auto_tag_service = None
        self.db = None  # 数据库连接

        # AI状态
        self.initialization_status = "not_initialized"  # not_initialized, initializing, ready, error
        self.last_error = None

    def initialize(self, config: Dict, tag_service=None, auto_tag_service=None, db=None) -> bool:
        """初始化AI功能"""
        try:
            self.initialization_status = "initializing"
            logger.info("开始初始化AI管理器")

            # 读取AI配置 - 支持用户库分离模式
            self.enabled = config.get("advanced", {}).get("enable_ai_features", False)

            # 优先从用户库配置读取AI配置
            library_path = config.get("library_path", "")
            if library_path:
                try:
                    from smartvault.services.library_config_service import get_library_config_service
                    library_service = get_library_config_service(library_path)
                    library_config = library_service.load_library_config()
                    ai_config = library_config.get("ai", {})
                    logger.info(f"从用户库配置加载AI配置: {library_path}")
                except Exception as e:
                    logger.warning(f"从用户库配置加载AI配置失败，使用全局配置: {e}")
                    ai_config = config.get("ai", {})
            else:
                ai_config = config.get("ai", {})

            logger.info(f"完整AI配置: {ai_config}")

            if not isinstance(ai_config, dict):
                logger.error("AI配置格式错误，应为字典")
                ai_config = {}

            # 设置服务引用
            self.tag_service = tag_service
            self.auto_tag_service = auto_tag_service
            self.db = db

            if not self.enabled:
                logger.info("AI功能未启用")
                self.initialization_status = "ready"
                return True

            # 初始化智能规则引擎（阶段一）
            self.smart_rule_engine = SmartRuleEngine()
            self.smart_rule_engine.initialize(config, tag_service, db)

            # 初始化自适应规则引擎（第二阶段）
            self.adaptive_rule_engine = AdaptiveRuleEngine()
            self.adaptive_rule_engine.initialize(db)

            # 根据配置决定AI能力级别 - 修正配置字段路径
            ml_config = ai_config.get("features", {}).get("ml_basic", {})
            ml_enabled = ml_config.get("enabled", False)
            logger.info(f"ML配置: {ml_config}, 启用状态: {ml_enabled}")

            if ml_enabled:
                logger.info("启用ML引擎")
                self.current_stage = "ml_basic"
                from .ml_engine import MLEngine

                # 处理模型路径 - 支持用户库分离模式
                model_path = ml_config.get("model_path", "")
                if model_path:
                    # 获取用户库路径
                    library_path = config.get("library_path", "")
                    resolved_model_path = self._resolve_model_path(model_path, library_path)

                    logger.info(f"原始模型路径: {model_path}")
                    logger.info(f"用户库路径: {library_path}")
                    logger.info(f"解析后的模型路径: {resolved_model_path}")
                    logger.info(f"模型文件存在: {os.path.exists(resolved_model_path)}")

                    if os.path.exists(resolved_model_path):
                        logger.info(f"找到模型文件: {resolved_model_path}")
                        self.ml_engine = MLEngine(model_path=resolved_model_path)
                        self.ml_engine.load_model()
                        if not self.ml_engine.is_ready():
                            logger.error("ML引擎加载失败")
                            self.current_stage = "rule_based"
                    else:
                        logger.error(f"模型文件不存在: {resolved_model_path}")
                        self.current_stage = "rule_based"
                else:
                    logger.warning("未配置模型路径")
                    self.current_stage = "rule_based"

            logger.info(f"AI阶段最终设置为: {self.current_stage}")
            self.initialization_status = "ready"
            return True

        except Exception as e:
            self.last_error = str(e)
            self.initialization_status = "error"
            logger.error(f"AI管理器初始化失败: {e}", exc_info=True)
            return False

    def _resolve_model_path(self, model_path: str, library_path: str) -> str:
        """解析模型路径，支持用户库分离模式

        Args:
            model_path: 配置中的模型路径
            library_path: 用户库路径

        Returns:
            str: 解析后的绝对路径
        """
        if not model_path:
            return ""

        # 如果已经是绝对路径，直接使用
        if os.path.isabs(model_path):
            return os.path.abspath(model_path)

        # 如果是相对路径，优先基于用户库路径解析
        if library_path:
            # 在用户库下创建AI模型目录
            ai_models_dir = os.path.join(library_path, "ai_models")
            os.makedirs(ai_models_dir, exist_ok=True)
            resolved_path = os.path.join(ai_models_dir, model_path)
            if os.path.exists(resolved_path):
                return os.path.abspath(resolved_path)

        # 如果用户库路径下没有找到，尝试基于项目根目录解析（向后兼容）
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        fallback_path = os.path.join(base_dir, model_path)
        return os.path.abspath(fallback_path)

    def is_available(self) -> bool:
        """检查AI功能是否可用"""
        return self.enabled and self.initialization_status == "ready"

    def get_status(self) -> Dict[str, Any]:
        """获取AI功能状态"""
        ml_ready = False
        ml_reason = ""

        # 检查ML引擎状态
        if self.current_stage == "ml_basic":
            if self.ml_engine:
                ml_ready = self.ml_engine.is_ready()
                if not ml_ready:
                    ml_reason = "模型加载失败"
                logger.info(f"ML引擎状态检查: ready={ml_ready}")
            else:
                ml_reason = "ML引擎未初始化"
        else:
            # 检查配置是否启用了ML但未能初始化
            from smartvault.utils.config import load_config
            config = load_config()
            ai_config = config.get("ai", {})
            ml_config = ai_config.get("features", {}).get("ml_basic", {})
            if ml_config.get("enabled", False):
                ml_reason = "配置启用但初始化失败"
            else:
                ml_reason = "配置未启用"

        return {
            "enabled": self.enabled,
            "stage": self.current_stage,
            "status": self.initialization_status,
            "last_error": self.last_error,
            "ml_enabled": self.current_stage == "ml_basic",
            "ml_model_loaded": ml_ready,
            "ml_reason": ml_reason,
            "features": {
                "smart_rules": self.smart_rule_engine is not None,
                "adaptive_rules": self.adaptive_rule_engine is not None,
                "ml_engine": ml_ready,
                "deep_learning": False
            }
        }

    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息"""
        stats = {}
        try:
            if self.smart_rule_engine and hasattr(self.smart_rule_engine, 'get_learning_statistics'):
                stats['behavior_learning'] = self.smart_rule_engine.get_learning_statistics()
            if self.adaptive_rule_engine and hasattr(self.adaptive_rule_engine, 'get_performance_statistics'):
                stats['rule_performance'] = self.adaptive_rule_engine.get_performance_statistics()
        except Exception as e:
            logger.error(f"获取学习统计失败: {e}")
        return stats

    def get_rule_performance_stats(self) -> Dict:
        """获取规则性能统计"""
        try:
            if self.adaptive_rule_engine and hasattr(self.adaptive_rule_engine, 'get_performance_statistics'):
                return self.adaptive_rule_engine.get_performance_statistics()
            return {}
        except Exception as e:
            logger.error(f"获取规则性能统计失败: {e}")
            return {}

    def suggest_tags(self, file_info: Dict) -> List[str]:
        """智能标签建议"""
        if not self.is_available():
            return self.fallback_service.suggest_tags_fallback(file_info)

        suggestions = []
        try:
            if self.smart_rule_engine:
                suggestions.extend(self.smart_rule_engine.suggest_tags(file_info))

            if self.ml_engine and self.current_stage in ["ml_basic", "deep_learning"]:
                suggestions.extend(self.ml_engine.predict_tags(file_info))

            return list(dict.fromkeys(suggestions))
        except Exception as e:
            logger.error(f"AI标签建议失败: {e}")
            return self.fallback_service.suggest_tags_fallback(file_info)
