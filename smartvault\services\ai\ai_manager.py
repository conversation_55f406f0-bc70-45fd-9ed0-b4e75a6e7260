"""
AI功能统一管理器

负责协调所有AI功能，提供统一的接口给其他服务使用
"""

import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .fallback_service import FallbackService
from .smart_rule_engine import SmartRuleEngine
from .adaptive_rule_engine import AdaptiveRuleEngine

logger = logging.getLogger(__name__)

class AIManager:
    """AI功能统一管理器"""

    def __init__(self):
        self.enabled = False
        self.current_stage = "rule_based"  # rule_based, ml_basic, deep_learning
        self.smart_rule_engine = None
        self.adaptive_rule_engine = None
        self.ml_engine = None
        self.fallback_service = FallbackService()

        # 与现有服务的集成
        self.tag_service = None
        self.auto_tag_service = None
        self.db = None  # 数据库连接

        # AI状态
        self.initialization_status = "not_initialized"  # not_initialized, initializing, ready, error
        self.last_error = None

    def initialize(self, config: Dict, tag_service=None, auto_tag_service=None, db=None) -> bool:
        """初始化AI功能"""
        try:
            self.initialization_status = "initializing"
            logger.info("开始初始化AI管理器")

            # 读取AI配置 - 使用advanced下的配置
            self.enabled = config.get("advanced", {}).get("enable_ai_features", False)
            ai_config = config.get("advanced", {}).get("ai", {}) or {}
            logger.info(f"完整AI配置: {config.get('advanced', {}).get('ai', {})}")
            logger.info(f"enable_ml实际值: {ai_config.get('enable_ml')}")

            if not isinstance(ai_config, dict):
                logger.error("AI配置格式错误，应为字典")
                ai_config = {}

            # 设置服务引用
            self.tag_service = tag_service
            self.auto_tag_service = auto_tag_service
            self.db = db

            if not self.enabled:
                logger.info("AI功能未启用")
                self.initialization_status = "ready"
                return True

            # 初始化智能规则引擎（阶段一）
            self.smart_rule_engine = SmartRuleEngine()
            self.smart_rule_engine.initialize(config, tag_service, db)

            # 初始化自适应规则引擎（第二阶段）
            self.adaptive_rule_engine = AdaptiveRuleEngine()
            self.adaptive_rule_engine.initialize(db)

            # 根据配置决定AI能力级别
            if ai_config.get("enable_ml", False):
                logger.info("启用ML引擎")
                self.current_stage = "ml_basic"
                from .ml_engine import MLEngine
                # 处理模型路径 - 转换为绝对路径
                model_path = ai_config.get("ml_model_path")
                if model_path:
                    # 优先尝试作为绝对路径
                    if not os.path.isabs(model_path):
                        # 如果是相对路径，基于项目根目录解析
                        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                        model_path = os.path.join(base_dir, model_path)
                    
                    model_path = os.path.abspath(model_path)
                    logger.info(f"解析后的模型绝对路径: {model_path}")
                    logger.info(f"模型文件存在: {os.path.exists(model_path)}")
                    logger.info(f"模型文件可读: {os.access(model_path, os.R_OK)}")
                    
                    if os.path.exists(model_path):
                        logger.info(f"找到模型文件: {model_path}")
                        self.ml_engine = MLEngine(model_path=model_path)
                        self.ml_engine.load_model()
                        if not self.ml_engine.is_ready():
                            logger.error("ML引擎加载失败")
                            self.current_stage = "rule_based"
                    else:
                        logger.error(f"模型文件不存在: {model_path}")
                        self.current_stage = "rule_based"

            logger.info(f"AI阶段最终设置为: {self.current_stage}")
            self.initialization_status = "ready"
            return True

        except Exception as e:
            self.last_error = str(e)
            self.initialization_status = "error"
            logger.error(f"AI管理器初始化失败: {e}", exc_info=True)
            return False

    def is_available(self) -> bool:
        """检查AI功能是否可用"""
        return self.enabled and self.initialization_status == "ready"

    def get_status(self) -> Dict[str, Any]:
        """获取AI功能状态"""
        ml_ready = False
        if self.current_stage == "ml_basic" and self.ml_engine:
            ml_ready = self.ml_engine.is_ready()
            logger.info(f"ML引擎状态检查: ready={ml_ready}")

        return {
            "enabled": self.enabled,
            "stage": self.current_stage,
            "status": self.initialization_status,
            "last_error": self.last_error,
            "features": {
                "smart_rules": self.smart_rule_engine is not None,
                "adaptive_rules": self.adaptive_rule_engine is not None,
                "ml_engine": ml_ready,
                "deep_learning": False
            }
        }

    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息"""
        stats = {}
        try:
            if self.smart_rule_engine and hasattr(self.smart_rule_engine, 'get_learning_statistics'):
                stats['behavior_learning'] = self.smart_rule_engine.get_learning_statistics()
            if self.adaptive_rule_engine and hasattr(self.adaptive_rule_engine, 'get_performance_statistics'):
                stats['rule_performance'] = self.adaptive_rule_engine.get_performance_statistics()
        except Exception as e:
            logger.error(f"获取学习统计失败: {e}")
        return stats

    def get_rule_performance_stats(self) -> Dict:
        """获取规则性能统计"""
        try:
            if self.adaptive_rule_engine and hasattr(self.adaptive_rule_engine, 'get_performance_statistics'):
                return self.adaptive_rule_engine.get_performance_statistics()
            return {}
        except Exception as e:
            logger.error(f"获取规则性能统计失败: {e}")
            return {}

    def suggest_tags(self, file_info: Dict) -> List[str]:
        """智能标签建议"""
        if not self.is_available():
            return self.fallback_service.suggest_tags_fallback(file_info)

        suggestions = []
        try:
            if self.smart_rule_engine:
                suggestions.extend(self.smart_rule_engine.suggest_tags(file_info))
            
            if self.ml_engine and self.current_stage in ["ml_basic", "deep_learning"]:
                suggestions.extend(self.ml_engine.predict_tags(file_info))
            
            return list(dict.fromkeys(suggestions))
        except Exception as e:
            logger.error(f"AI标签建议失败: {e}")
            return self.fallback_service.suggest_tags_fallback(file_info)
