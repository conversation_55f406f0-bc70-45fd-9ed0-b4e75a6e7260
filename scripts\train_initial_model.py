import os
import joblib
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.pipeline import Pipeline

def train_initial_model():
    """训练并保存初始ML模型"""
    try:
        # 创建简单的训练数据
        train_data = [
            ("project_report.pdf", "文档"),
            ("meeting_notes.txt", "文档"), 
            ("python_script.py", "代码"),
            ("main.py", "代码"),
            ("image1.jpg", "图片"),
            ("photo.png", "图片"),
            ("data.csv", "数据"),
            ("results.xlsx", "数据")
        ]

        # 准备训练集
        X_train = [x[0] for x in train_data]
        y_train = [x[1] for x in train_data]

        # 创建特征提取器
        vectorizer = TfidfVectorizer(analyzer='char', ngram_range=(1, 3))
        
        # 创建分类器
        classifier = RandomForestClassifier(n_estimators=50)
        
        # 创建模型管道
        model = Pipeline([
            ('tfidf', vectorizer),
            ('clf', classifier)
        ])

        # 训练模型
        model.fit(X_train, y_train)

        # 确保模型目录存在
        os.makedirs("smartvault/services/ai/models/lightweight", exist_ok=True)

        # 保存模型
        model_path = "smartvault/services/ai/models/lightweight/tag_predictor.joblib"
        joblib.dump(model, model_path)

        print(f"✅ 初始模型已训练并保存到: {model_path}")
        print("🔄 模型可以预测的文件类型:", set(y_train))
        return True
    except Exception as e:
        print(f"❌ 训练初始模型时出错: {e}")
        return False

if __name__ == "__main__":
    train_initial_model()