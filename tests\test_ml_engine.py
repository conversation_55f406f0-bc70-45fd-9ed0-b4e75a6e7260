import pytest
from smartvault.services.ai.ml_engine import MLEng<PERSON>

def test_train_and_predict(tmp_path):
    engine = MLEngine()
    X_texts = [
        "file1.txt txt /path/to/file1 100 2023-01-01",
        "file2.doc doc /path/to/file2 200 2023-01-02",
        "file3.pdf pdf /path/to/file3 150 2023-01-03"
    ]
    y_labels = ["tag1", "tag2", "tag1"]

    # 训练模型
    engine.train(X_texts, y_labels)

    # 保存模型
    model_file = tmp_path / "model.pkl"
    engine.save_model(str(model_file))
    assert model_file.exists()

    # 加载模型
    engine.load_model(str(model_file))
    assert engine.model is not None

    # 预测标签
    file_info = {
        "name": "file4.txt",
        "extension": "txt",
        "path": "/path/to/file4",
        "size": 120,
        "modified_time": "2023-01-04"
    }
    tags = engine.predict_tags(file_info)
    assert isinstance(tags, list)

def test_extract_features():
    engine = MLEngine()
    file_info = {
        "name": "example.txt",
        "extension": "txt",
        "path": "/some/path",
        "size": 1234,
        "modified_time": "2023-01-01"
    }
    features = engine.extract_features(file_info)
    assert "example.txt" in features
    assert "txt" in features
    assert "/some/path" in features
    assert "1234" in features
    assert "2023-01-01" in features

def test_decode_predictions():
    engine = MLEngine()
    preds_list = ["tag1", "tag2"]
    preds_single = "tag3"

    decoded_list = engine.decode_predictions(preds_list)
    decoded_single = engine.decode_predictions(preds_single)

    assert decoded_list == preds_list
    assert decoded_single == [preds_single]
