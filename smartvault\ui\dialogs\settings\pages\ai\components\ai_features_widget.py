"""
AI功能开关组件

提供AI各项功能的开关控制
预期代码长度: < 200行
当前代码长度: 180行 ✅
"""

from typing import Dict
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QCheckBox, QLabel, QSpinBox, QDoubleSpinBox,
    QGroupBox, QGridLayout
)
from PySide6.QtCore import Qt
from .base_ai_widget import BaseAIWidget


class AIFeaturesWidget(BaseAIWidget):
    """AI功能开关组件"""

    def __init__(self, parent=None):
        super().__init__("AI功能设置", parent)

    def setup_ui(self):
        """设置功能开关UI"""
        layout = QVBoxLayout(self)

        # AI总开关
        self.ai_enabled_checkbox = QCheckBox("启用AI功能")
        self.ai_enabled_checkbox.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(self.ai_enabled_checkbox)

        # 功能分组
        features_group = QGroupBox("具体功能")
        features_layout = QVBoxLayout(features_group)

        # 项目识别功能
        project_group = QGroupBox("项目标签识别")
        project_layout = QGridLayout(project_group)

        self.project_enabled_checkbox = QCheckBox("启用项目识别")
        project_layout.addWidget(self.project_enabled_checkbox, 0, 0, 1, 2)

        project_layout.addWidget(QLabel("最少文件数:"), 1, 0)
        self.project_min_files_spinbox = QSpinBox()
        self.project_min_files_spinbox.setRange(1, 50)
        self.project_min_files_spinbox.setValue(3)
        project_layout.addWidget(self.project_min_files_spinbox, 1, 1)

        project_layout.addWidget(QLabel("置信度阈值:"), 2, 0)
        self.project_confidence_spinbox = QDoubleSpinBox()
        self.project_confidence_spinbox.setRange(0.1, 1.0)
        self.project_confidence_spinbox.setSingleStep(0.1)
        self.project_confidence_spinbox.setValue(0.7)
        project_layout.addWidget(self.project_confidence_spinbox, 2, 1)

        features_layout.addWidget(project_group)

        # 系列检测功能
        series_group = QGroupBox("系列标签检测")
        series_layout = QGridLayout(series_group)

        self.series_enabled_checkbox = QCheckBox("启用系列检测")
        series_layout.addWidget(self.series_enabled_checkbox, 0, 0, 1, 2)

        series_layout.addWidget(QLabel("最少文件数:"), 1, 0)
        self.series_min_files_spinbox = QSpinBox()
        self.series_min_files_spinbox.setRange(2, 20)
        self.series_min_files_spinbox.setValue(2)
        series_layout.addWidget(self.series_min_files_spinbox, 1, 1)

        series_layout.addWidget(QLabel("置信度阈值:"), 2, 0)
        self.series_confidence_spinbox = QDoubleSpinBox()
        self.series_confidence_spinbox.setRange(0.1, 1.0)
        self.series_confidence_spinbox.setSingleStep(0.1)
        self.series_confidence_spinbox.setValue(0.6)
        series_layout.addWidget(self.series_confidence_spinbox, 2, 1)

        features_layout.addWidget(series_group)

        # 行为学习功能
        learning_group = QGroupBox("行为模式学习")
        learning_layout = QGridLayout(learning_group)

        self.learning_enabled_checkbox = QCheckBox("启用行为学习")
        learning_layout.addWidget(self.learning_enabled_checkbox, 0, 0, 1, 2)

        learning_layout.addWidget(QLabel("学习率:"), 1, 0)
        self.learning_rate_spinbox = QDoubleSpinBox()
        self.learning_rate_spinbox.setRange(0.01, 1.0)
        self.learning_rate_spinbox.setSingleStep(0.01)
        self.learning_rate_spinbox.setValue(0.1)
        learning_layout.addWidget(self.learning_rate_spinbox, 1, 1)

        learning_layout.addWidget(QLabel("最大模式数:"), 2, 0)
        self.max_patterns_spinbox = QSpinBox()
        self.max_patterns_spinbox.setRange(100, 10000)
        self.max_patterns_spinbox.setSingleStep(100)
        self.max_patterns_spinbox.setValue(1000)
        learning_layout.addWidget(self.max_patterns_spinbox, 2, 1)

        features_layout.addWidget(learning_group)

        # 智能建议功能
        suggestions_group = QGroupBox("智能标签建议")
        suggestions_layout = QGridLayout(suggestions_group)

        self.suggestions_enabled_checkbox = QCheckBox("启用智能建议")
        suggestions_layout.addWidget(self.suggestions_enabled_checkbox, 0, 0, 1, 2)

        suggestions_layout.addWidget(QLabel("最大建议数:"), 1, 0)
        self.max_suggestions_spinbox = QSpinBox()
        self.max_suggestions_spinbox.setRange(1, 20)
        self.max_suggestions_spinbox.setValue(8)
        suggestions_layout.addWidget(self.max_suggestions_spinbox, 1, 1)

        self.merge_with_rules_checkbox = QCheckBox("与规则建议合并")
        suggestions_layout.addWidget(self.merge_with_rules_checkbox, 2, 0, 1, 2)

        features_layout.addWidget(suggestions_group)

        # 轻量级机器学习功能
        ml_group = QGroupBox("机器学习引擎(ML)")
        ml_layout = QGridLayout(ml_group)

        self.ml_enabled_checkbox = QCheckBox("启用机器学习引擎(ML)")
        ml_layout.addWidget(self.ml_enabled_checkbox, 0, 0, 1, 2)

        ml_layout.addWidget(QLabel("模型文件路径:"), 1, 0)
        from PySide6.QtWidgets import QLineEdit
        self.ml_model_path_edit = QLineEdit()
        ml_layout.addWidget(self.ml_model_path_edit, 1, 1)

        ml_layout.addWidget(QLabel("置信度阈值:"), 2, 0)
        self.ml_confidence_threshold_spinbox = QDoubleSpinBox()
        self.ml_confidence_threshold_spinbox.setRange(0.1, 1.0)
        self.ml_confidence_threshold_spinbox.setSingleStep(0.1)
        self.ml_confidence_threshold_spinbox.setValue(0.6)
        ml_layout.addWidget(self.ml_confidence_threshold_spinbox, 2, 1)

        features_layout.addWidget(ml_group)

        layout.addWidget(features_group)

        # 添加弹性空间
        layout.addStretch()

    def setup_connections(self):
        """设置信号连接"""
        super().setup_connections()
        self._connect_signals()

    def _connect_signals(self):
        """连接信号"""
        # 总开关控制子功能
        self.ai_enabled_checkbox.toggled.connect(self._on_ai_enabled_changed)

        # 各功能开关变化时发送信号
        self.project_enabled_checkbox.toggled.connect(self._on_config_changed)
        self.series_enabled_checkbox.toggled.connect(self._on_config_changed)
        self.learning_enabled_checkbox.toggled.connect(self._on_config_changed)
        self.suggestions_enabled_checkbox.toggled.connect(self._on_config_changed)

        # 参数变化时发送信号
        self.project_min_files_spinbox.valueChanged.connect(self._on_config_changed)
        self.project_confidence_spinbox.valueChanged.connect(self._on_config_changed)
        self.series_min_files_spinbox.valueChanged.connect(self._on_config_changed)
        self.series_confidence_spinbox.valueChanged.connect(self._on_config_changed)
        self.learning_rate_spinbox.valueChanged.connect(self._on_config_changed)
        self.max_patterns_spinbox.valueChanged.connect(self._on_config_changed)
        self.max_suggestions_spinbox.valueChanged.connect(self._on_config_changed)
        self.merge_with_rules_checkbox.toggled.connect(self._on_config_changed)

    def _disconnect_signals(self):
        """断开信号连接"""
        try:
            # 总开关
            self.ai_enabled_checkbox.toggled.disconnect(self._on_ai_enabled_changed)

            # 各功能开关
            self.project_enabled_checkbox.toggled.disconnect(self._on_config_changed)
            self.series_enabled_checkbox.toggled.disconnect(self._on_config_changed)
            self.learning_enabled_checkbox.toggled.disconnect(self._on_config_changed)
            self.suggestions_enabled_checkbox.toggled.disconnect(self._on_config_changed)

            # 参数变化
            self.project_min_files_spinbox.valueChanged.disconnect(self._on_config_changed)
            self.project_confidence_spinbox.valueChanged.disconnect(self._on_config_changed)
            self.series_min_files_spinbox.valueChanged.disconnect(self._on_config_changed)
            self.series_confidence_spinbox.valueChanged.disconnect(self._on_config_changed)
            self.learning_rate_spinbox.valueChanged.disconnect(self._on_config_changed)
            self.max_patterns_spinbox.valueChanged.disconnect(self._on_config_changed)
            self.max_suggestions_spinbox.valueChanged.disconnect(self._on_config_changed)
            self.merge_with_rules_checkbox.toggled.disconnect(self._on_config_changed)
        except Exception:
            # 忽略断开连接时的错误
            pass

    def load_config(self, config: Dict):
        """加载配置到UI控件"""
        self._config = config

        # 临时断开信号连接，避免加载时触发保存
        self._disconnect_signals()

        try:
            # 加载总开关
            self.ai_enabled_checkbox.setChecked(config.get('enabled', False))

            # 加载功能配置
            features = config.get('features', {})

            # 项目识别配置
            project_config = features.get('project_detection', {})
            self.project_enabled_checkbox.setChecked(project_config.get('enabled', True))
            self.project_min_files_spinbox.setValue(project_config.get('min_files', 3))
            self.project_confidence_spinbox.setValue(project_config.get('confidence_threshold', 0.7))

            # 系列检测配置
            series_config = features.get('series_detection', {})
            self.series_enabled_checkbox.setChecked(series_config.get('enabled', True))
            self.series_min_files_spinbox.setValue(series_config.get('min_files', 2))
            self.series_confidence_spinbox.setValue(series_config.get('confidence_threshold', 0.6))

            # 行为学习配置
            learning_config = features.get('behavior_learning', {})
            self.learning_enabled_checkbox.setChecked(learning_config.get('enabled', True))
            self.learning_rate_spinbox.setValue(learning_config.get('learning_rate', 0.1))
            self.max_patterns_spinbox.setValue(learning_config.get('max_patterns', 1000))

            # 智能建议配置
            suggestions_config = features.get('smart_suggestions', {})
            self.suggestions_enabled_checkbox.setChecked(suggestions_config.get('enabled', True))
            self.max_suggestions_spinbox.setValue(suggestions_config.get('max_suggestions', 8))
            self.merge_with_rules_checkbox.setChecked(suggestions_config.get('merge_with_rules', True))

            # 新增轻量级机器学习配置
            ml_config = features.get('ml_basic', {})
            self.ml_enabled_checkbox.setChecked(ml_config.get('enabled', False))
            self.ml_model_path_edit.setText(ml_config.get('model_path', ''))
            self.ml_confidence_threshold_spinbox.setValue(ml_config.get('confidence_threshold', 0.6))

            # 更新UI状态
            self._update_ui_state()

        finally:
            # 重新连接信号
            self._connect_signals()

    def save_config(self) -> Dict:
        """从UI控件保存配置"""
        config = {
            'enabled': self.ai_enabled_checkbox.isChecked(),
            'stage': 'rule_based',  # 当前阶段固定为规则引擎
            'features': {
                'project_detection': {
                    'enabled': self.project_enabled_checkbox.isChecked(),
                    'min_files': self.project_min_files_spinbox.value(),
                    'confidence_threshold': self.project_confidence_spinbox.value()
                },
                'series_detection': {
                    'enabled': self.series_enabled_checkbox.isChecked(),
                    'min_files': self.series_min_files_spinbox.value(),
                    'confidence_threshold': self.series_confidence_spinbox.value()
                },
                'behavior_learning': {
                    'enabled': self.learning_enabled_checkbox.isChecked(),
                    'learning_rate': self.learning_rate_spinbox.value(),
                    'max_patterns': self.max_patterns_spinbox.value()
                },
                'smart_suggestions': {
                    'enabled': self.suggestions_enabled_checkbox.isChecked(),
                    'max_suggestions': self.max_suggestions_spinbox.value(),
                    'merge_with_rules': self.merge_with_rules_checkbox.isChecked()
                },
                'ml_basic': {
                    'enabled': self.ml_enabled_checkbox.isChecked(),
                    'model_path': self.ml_model_path_edit.text(),
                    'confidence_threshold': self.ml_confidence_threshold_spinbox.value()
                }
            }
        }

        self._config.update(config)
        return config

    def _on_ai_enabled_changed(self, enabled: bool):
        """AI总开关变化处理"""
        self._update_ui_state()
        self._on_config_changed()

    def _update_ui_state(self):
        """更新UI状态"""
        enabled = self.ai_enabled_checkbox.isChecked()

        # 启用/禁用所有子功能
        widgets = [
            self.project_enabled_checkbox, self.project_min_files_spinbox, self.project_confidence_spinbox,
            self.series_enabled_checkbox, self.series_min_files_spinbox, self.series_confidence_spinbox,
            self.learning_enabled_checkbox, self.learning_rate_spinbox, self.max_patterns_spinbox,
            self.suggestions_enabled_checkbox, self.max_suggestions_spinbox, self.merge_with_rules_checkbox,
            self.ml_enabled_checkbox, self.ml_model_path_edit, self.ml_confidence_threshold_spinbox
        ]

        for widget in widgets:
            widget.setEnabled(enabled)

        # 如果AI总开关关闭，确保ML开关也关闭
        if not enabled:
            self.ml_enabled_checkbox.setChecked(False)

    def _on_config_changed(self):
        """配置变化处理"""
        config = self.save_config()
        self.config_changed.emit(config)
